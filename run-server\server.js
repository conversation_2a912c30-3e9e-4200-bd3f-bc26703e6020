const express = require('express')
const cors = require('cors')
const bodyParser = require('body-parser')
const cookieParser = require('cookie-parser')
const http = require('http')
const fileWatcher = require('./services/fileWatcher')
const imageSplitWatcher = require('./services/imageSplitWatcher')
const wsServer = require('./services/wsServer')
const urlMonitor = require('./services/urlMonitorWithDB')
const configStore = require('./services/configStore')
const authProxy = require('./services/authProxy')
const { defaultLogger } = require('./utils/logger')
const { EventEmitter } = require('events')

// Create a global event emitter for authentication events
const authEvents = new EventEmitter()

/**
 * Function to start the server - exports this function instead of immediately starting
 * This allows main.js to directly require and start the server without forking a new process
 * @param {Object} options - Server options
 * @param {number} options.port - Port to run the server on (default: 3000)
 * @returns {Object} - Server instance and app instance
 */
function startServer(options = {}) {
  const app = express()
  const port = options.port || process.env.PORT || 3000
  // 创建HTTP服务器
  const server = http.createServer(app)
  defaultLogger.writeInitialLog()

  // CORS配置选项
  const corsOptions = {
    origin: function (origin, callback) {
      // 允许所有来源，或者指定来源列表
      // 例如: ['http://example.com', 'https://localhost:8080']
      callback(null, true)
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
    optionsSuccessStatus: 200 // 针对旧版浏览器兼容
  }

  // 中间件
  app.use(cors(corsOptions)) // 使用自定义CORS配置
  app.use(bodyParser.json()) // 解析JSON请求体
  app.use(bodyParser.urlencoded({ extended: true }))
  app.use(cookieParser()) // 解析Cookie

  // 健康检查路由
  app.get('/', (req, res) => {
    res.json({ message: 'WPS Add-on Server is running' })
  })

  // WebSocket连接统计API
  app.get('/api/ws-stats', (req, res) => {
    try {
      // 获取WebSocket服务状态
      const wsStats = wsServer.getStats()

      // 详细客户端信息 - 敏感信息，仅在请求中包含detail参数时返回
      if (req.query.detail === 'true') {
        wsStats.clients = wsServer.getClientDetails()
      }

      res.json(wsStats)
    } catch (error) {
      defaultLogger.error('获取WebSocket统计信息失败:', error)
      res.status(500).json({
        success: false,
        error: '获取统计信息失败',
        message: error.message
      })
    }
  })

  // WebSocket设置API
  app.post('/api/ws-config', (req, res) => {
    try {
      const { logLevel } = req.body

      // 设置日志级别
      if (logLevel) {
        const success = wsServer.setLogLevel(logLevel)
        if (!success) {
          return res.status(400).json({
            success: false,
            message: `无效的日志级别: ${logLevel}。有效值: error, warn, info, debug`
          })
        }
      }

      res.json({
        success: true,
        message: '配置已更新',
        currentLogLevel: wsServer.logLevel
      })
    } catch (error) {
      defaultLogger.error('更新WebSocket配置失败:', error)
      res.status(500).json({
        success: false,
        error: '更新配置失败',
        message: error.message
      })
    }
  })

  // 客户端日志上报API
  app.post('/api/client-log', (req, res) => {
    try {
      const { level, message, data } = req.body
      defaultLogger.info(`客户端日志 [${level || 'info'}]: ${message}`, data)
      res.json({ success: true })
    } catch (error) {
      defaultLogger.error('处理客户端日志失败:', error)
      res.status(500).json({ success: false, error: '处理日志失败' })
    }
  })

  // 详细的健康检查和诊断API
  app.get('/api/health', (req, res) => {
    const startTime = Date.now()

    // 获取所有服务状态
    const watcherStatus = fileWatcher.getStatus()
    const urlMonitorStatus = urlMonitor.getStatus()

    // 收集系统信息
    const systemInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    }

    // 网络接口信息
    const os = require('os')
    const networkInterfaces = os.networkInterfaces()

    // 仅包含服务器地址信息，不包含所有网络细节（出于安全考虑）
    const serverAddress = {
      hostname: os.hostname(),
      interfaces: Object.keys(networkInterfaces).reduce((acc, name) => {
        acc[name] = networkInterfaces[name]
          .filter((iface) => !iface.internal)
          .map((iface) => ({
            family: iface.family,
            address: iface.address
          }))
        return acc
      }, {})
    }

    // WebSocket服务状态
    const wsStatus = {
      running: wsServer.isRunning(),
      clientCount: wsServer.getClientCount()
    }

    // 配置服务状态
    const configStatus = {
      addonConfigPath: configStore.getAddonConfigPath(),
      downloadPath: configStore.getDownloadPath()
    }

    // 检查文件系统权限（简单测试）
    let fsPermissions = { readable: false, writable: false, error: null }
    try {
      const fs = require('fs')
      const path = require('path')
      const testDir = configStore.getAddonConfigPath() || os.tmpdir()
      const testFile = path.join(testDir, '.health-check-test')

      // 尝试写入测试文件
      fs.writeFileSync(testFile, 'test', { flag: 'w' })
      fsPermissions.writable = true

      // 尝试读取测试文件
      fs.readFileSync(testFile, 'utf8')
      fsPermissions.readable = true

      // 删除测试文件
      fs.unlinkSync(testFile)
    } catch (error) {
      fsPermissions.error = error.message
    }

    // 响应耗时
    const duration = Date.now() - startTime

    // 计算WebSocket服务连接统计
    const wsConnectionStats = {
      activeConnections: wsServer.getClientCount(),
      logLevel: wsServer.logLevel
    }

    // 如果请求包含ws_detail参数，添加更多WebSocket详情
    if (req.query.ws_detail === 'true') {
      const wsStats = wsServer.getStats()
      wsConnectionStats.stats = {
        connections: wsStats.connections,
        handlers: wsStats.handlers
      }
    }

    // 返回完整健康状态
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      duration: `${duration}ms`,
      services: {
        watcher: watcherStatus,
        urlMonitor: urlMonitorStatus,
        websocket: {
          ...wsStatus,
          connections: wsConnectionStats
        },
        config: configStatus
      },
      system: systemInfo,
      network: serverAddress,
      filesystem: fsPermissions
    })
  })

  // 错误处理中间件
  app.use((err, req, res, next) => {
    res.status(500).json({
      message: 'Internal Server Error',
      error: process.env.NODE_ENV === 'development' ? err.message : undefined
    })
  })

  // 初始化WebSocket服务器
  wsServer.initialize(server)

  // 设置版本信息提供者
  wsServer.setVersionInfoProvider(() => {
    // 从main.js的ENV配置获取版本信息
    return {
      edition: global.ENV?.config?.EDITION || 'wanwei',
      appVersion: global.ENV?.config?.APP_VERSION || '1.0.0',
      buildDate: global.ENV?.config?.BUILD_DATE || new Date().toISOString(),
      environment: global.ENV?.config?.ENV || 'development',
      bypassEncryption: global.ENV?.config?.BYPASS_ENCRYPTION || false
    };
  })

  // 添加客户端断开连接的处理
  wsServer.onClientDisconnected = (clientId) => {
    if (clientId) {
      // 清理客户端关联的URL监控任务
      urlMonitor.cleanupDisconnectedClient(clientId)

      // 清理客户端关联的文件监控任务
      fileWatcher.cleanupDisconnectedClient(clientId)

      // 清理客户端关联的图片分割监控任务
      imageSplitWatcher.cleanupDisconnectedClient(clientId)
    }
  }

  // 注册WebSocket消息处理器
  registerWsMessageHandlers()

  // 启动HTTP服务器和文件监控服务
  server.listen(port, () => {
    defaultLogger.info(`服务器运行在端口 ${port}`)
    // 自动启动文件监控服务
    fileWatcher.start()
    // 自动启动图片分割监控服务
    imageSplitWatcher.start()
  })

  // 注册所有WebSocket消息处理器
  function registerWsMessageHandlers() {
    // 文件监控服务消息处理
    wsServer.registerMessageHandler('watcher', (client, action, data) => {
      // 获取客户端信息以获取clientId
      const clientInfo = wsServer.clients.get(client)
      const clientId = clientInfo ? clientInfo.id : data.clientId || null

      switch (action) {
        case 'getStatus':
          const status = fileWatcher.getStatus()
          status.addonConfigPath = configStore.getAddonConfigPath()
          wsServer.sendToClient(client, {
            type: 'watcher',
            action: 'status',
            messageId: data.messageId,
            data: status
          })
          break

        case 'setDirectory':
          if (!data || !data.directory) {
            return wsServer.sendToClient(client, {
              type: 'watcher',
              action: 'setDirectory',
              success: false,
              messageId: data.messageId,
              message: '必须提供目录路径'
            })
          }

          const success = fileWatcher.setWatchDir(data.directory)
          wsServer.sendToClient(client, {
            type: 'watcher',
            action: 'setDirectory',
            success,
            messageId: data.messageId,
            directory: data.directory,
            message: success ? '设置监控目录成功' : '无法设置监控目录，可能服务正在运行'
          })
          break

        case 'start':
          fileWatcher.start()
          wsServer.sendToClient(client, {
            type: 'watcher',
            action: 'start',
            success: true,
            messageId: data.messageId,
            message: '文件监控服务已启动'
          })
          break

        case 'stop':
          fileWatcher.stop()
          wsServer.sendToClient(client, {
            type: 'watcher',
            action: 'stop',
            success: true,
            messageId: data.messageId,
            message: '文件监控服务已停止'
          })
          break

        case 'associateFile':
          if (!data || !data.fileName) {
            return wsServer.sendToClient(client, {
              type: 'watcher',
              action: 'associateFile',
              success: false,
              messageId: data.messageId,
              message: '必须提供文件名'
            })
          }

          // 关联文件与客户端
          if (clientId) {
            fileWatcher.associateFileWithClient(data.fileName, clientId)
            wsServer.sendToClient(client, {
              type: 'watcher',
              action: 'associateFile',
              success: true,
              messageId: data.messageId,
              fileName: data.fileName,
              message: '文件与客户端关联成功'
            })
          } else {
            wsServer.sendToClient(client, {
              type: 'watcher',
              action: 'associateFile',
              success: false,
              messageId: data.messageId,
              message: '无法获取客户端ID'
            })
          }
          break

        default:
          wsServer.sendToClient(client, {
            type: 'error',
            messageId: data.messageId,
            message: `未知的watcher操作: ${action}`
          })
      }
    })

    // 图片分割监控服务消息处理
    wsServer.registerMessageHandler('imageSplit', async (client, action, data) => {
      const clientInfo = wsServer.clients.get(client)
      const clientId = clientInfo ? clientInfo.id : data.clientId || null

      switch (action) {
        case 'getStatus':
          wsServer.sendToClient(client, {
            type: 'imageSplit',
            action: 'status',
            messageId: data.messageId,
            data: imageSplitWatcher.getStatus()
          })
          break

        case 'getWatchDir':
          wsServer.sendToClient(client, {
            type: 'imageSplit',
            action: 'watchDir',
            messageId: data.messageId,
            watchDir: imageSplitWatcher.getImageSplitDir()
          })
          break

        case 'associateFile':
          if (!data.fileName) {
            wsServer.sendToClient(client, {
              type: 'imageSplit',
              action: 'associateFile',
              success: false,
              messageId: data.messageId,
              message: '缺少fileName参数'
            })
          }

          // 关联图片文件与客户端
          if (clientId) {
            imageSplitWatcher.associateFileWithClient(data.fileName, clientId)
            wsServer.sendToClient(client, {
              type: 'imageSplit',
              action: 'associateFile',
              success: true,
              messageId: data.messageId,
              fileName: data.fileName,
              message: '图片文件与客户端关联成功'
            })
          } else {
            wsServer.sendToClient(client, {
              type: 'imageSplit',
              action: 'associateFile',
              success: false,
              messageId: data.messageId,
              message: '无法获取客户端ID'
            })
          }
          break

        case 'uploadFile':
          if (!data.fileName) {
            wsServer.sendToClient(client, {
              type: 'imageSplit',
              action: 'uploadFile',
              success: false,
              messageId: data.messageId,
              message: '缺少fileName参数'
            })
            break
          }

          try {
            const uploadResult = await imageSplitWatcher.triggerFileUpload(data.fileName, clientId)
            wsServer.sendToClient(client, {
              type: 'imageSplit',
              action: 'uploadFile',
              success: uploadResult.success,
              messageId: data.messageId,
              fileName: data.fileName,
              ossUrl: uploadResult.ossUrl,
              fileSize: uploadResult.fileSize,
              error: uploadResult.error,
              message: uploadResult.success ? '图片文件上传成功' : `图片文件上传失败: ${uploadResult.error}`
            })
          } catch (error) {
            wsServer.sendToClient(client, {
              type: 'imageSplit',
              action: 'uploadFile',
              success: false,
              messageId: data.messageId,
              fileName: data.fileName,
              error: 'uploadException',
              message: `图片文件上传异常: ${error.message}`
            })
          }
          break

        default:
          wsServer.sendToClient(client, {
            type: 'error',
            messageId: data.messageId,
            message: `未知的imageSplit操作: ${action}`
          })
      }
    })

    // URL监控服务消息处理
    wsServer.registerMessageHandler('urlMonitor', async (client, action, data) => {
      // 获取客户端信息以获取clientId
      const clientInfo = wsServer.clients.get(client)
      const clientId = clientInfo ? clientInfo.id : data.clientId || null

      switch (action) {
        case 'getStatus':
          wsServer.sendToClient(client, {
            type: 'urlMonitor',
            action: 'status',
            messageId: data.messageId,
            data: urlMonitor.getStatus(clientId) // 传递clientId参数
          })
          break

        case 'startMonitoring':
          if (!data || !data.url) {
            return wsServer.sendToClient(client, {
              type: 'urlMonitor',
              action: 'startMonitoring',
              success: false,
              messageId: data.messageId,
              message: '必须提供URL'
            })
          }

          const options = {
            downloadOnSuccess: data.downloadOnSuccess === undefined ? true : data.downloadOnSuccess,
            filename: data.filename,
            taskId: data.taskId,
            clientId: clientId // 添加客户端ID到选项
          }

          const urlId = await urlMonitor.startMonitoring(data.url, data.interval, options)
          wsServer.sendToClient(client, {
            type: 'urlMonitor',
            action: 'startMonitoring',
            success: !!urlId,
            messageId: data.messageId,
            urlId,
            url: data.url,
            interval: data.interval || 5000,
            options,
            message: urlId ? '开始监控URL成功' : '无法开始URL监控'
          })
          break

        case 'stopMonitoring':
          if (!data || !data.urlId) {
            return wsServer.sendToClient(client, {
              type: 'urlMonitor',
              action: 'stopMonitoring',
              success: false,
              messageId: data.messageId,
              message: '必须提供URL ID'
            })
          }

          const stopSuccess = urlMonitor.stopMonitoring(data.urlId, clientId) // 传递clientId参数

          wsServer.sendToClient(client, {
            type: 'urlMonitor',
            action: 'stopMonitoring',
            success: stopSuccess,
            messageId: data.messageId,
            urlId: data.urlId,
            message: stopSuccess ? '停止URL监控成功' : '找不到指定的URL监控任务'
          })
          break

        case 'forceCheck':
          if (!data || !data.urlId) {
            return wsServer.sendToClient(client, {
              type: 'urlMonitor',
              action: 'forceCheck',
              success: false,
              messageId: data.messageId,
              message: '必须提供URL ID'
            })
          }

          const checkSuccess = await urlMonitor.forceCheck(data.urlId, clientId) // 传递clientId参数

          wsServer.sendToClient(client, {
            type: 'urlMonitor',
            action: 'forceCheck',
            success: checkSuccess,
            messageId: data.messageId,
            urlId: data.urlId,
            message: checkSuccess ? '已触发URL检查' : '找不到指定的URL监控任务'
          })
          break

        case 'startChecking':
          if (!data || !data.urlId) {
            return wsServer.sendToClient(client, {
              type: 'urlMonitor',
              action: 'startChecking',
              success: false,
              messageId: data.messageId,
              message: '必须提供URL ID'
            })
          }
          const startSuccess = urlMonitor.startChecking(data.urlId, clientId) // 传递clientId参数

          wsServer.sendToClient(client, {
            type: 'urlMonitor',
            action: 'startChecking',
            success: startSuccess,
            messageId: data.messageId,
            urlId: data.urlId,
            message: startSuccess ? '已开始URL检查' : '找不到指定的URL监控任务'
          })
          break

        case 'forceDownload':
          if (!data || !data.urlId) {
            return wsServer.sendToClient(client, {
              type: 'urlMonitor',
              action: 'forceDownload',
              success: false,
              messageId: data.messageId,
              message: '必须提供URL ID'
            })
          }

          // 保存messageId以在异步回调中使用
          const downloadMessageId = data.messageId

          urlMonitor
            .forceDownload(data.urlId, clientId) // 传递clientId参数
            .then((success) => {
              wsServer.sendToClient(client, {
                type: 'urlMonitor',
                action: 'forceDownload',
                success,
                messageId: downloadMessageId,
                urlId: data.urlId,
                message: success ? '已触发文件下载' : '找不到指定的URL监控任务或下载失败'
              })
            })
            .catch((error) => {
              wsServer.sendToClient(client, {
                type: 'urlMonitor',
                action: 'forceDownload',
                success: false,
                messageId: downloadMessageId,
                urlId: data.urlId,
                message: `下载过程中发生错误: ${error.message}`
              })
            })
          break

        case 'getDownloadPath':
          const downloadPath = urlMonitor.getDownloadPath()
          wsServer.sendToClient(client, {
            type: 'urlMonitor',
            action: 'downloadPath',
            success: true,
            messageId: data.messageId,
            downloadPath
          })
          break

        case 'setDownloadPath':
          if (!data || !data.path) {
            return wsServer.sendToClient(client, {
              type: 'urlMonitor',
              action: 'setDownloadPath',
              success: false,
              messageId: data.messageId,
              message: '必须提供下载路径'
            })
          }

          const pathSuccess = urlMonitor.setDownloadPath(data.path, clientId) // 传递clientId参数

          wsServer.sendToClient(client, {
            type: 'urlMonitor',
            action: 'setDownloadPath',
            success: pathSuccess,
            messageId: data.messageId,
            downloadPath: data.path,
            message: pathSuccess ? '设置下载路径成功' : '无法设置下载路径'
          })
          break

        case 'resumeUrlMonitors':
          if (!data || !data.taskIds || !Array.isArray(data.taskIds)) {
            return wsServer.sendToClient(client, {
              type: 'urlMonitor',
              action: 'resumeUrlMonitors',
              success: false,
              messageId: data.messageId,
              message: '必须提供taskIds数组'
            })
          }
          const resumeResult = await urlMonitor.handleResumeUrlMonitors(data, clientId)
          wsServer.sendToClient(client, {
            type: 'urlMonitor',
            action: 'resumeUrlMonitors',
            success: resumeResult.success,
            messageId: data.messageId,
            message: resumeResult.message
          })
          break

        case 'updateTaskStatus':
          if (!data || !data.urlId) {
            return wsServer.sendToClient(client, {
              type: 'urlMonitor',
              action: 'updateTaskStatus',
              success: false,
              messageId: data.messageId,
              message: '必须提供urlId'
            })
          }

          try {
            // 更新URL任务状态为completed
            const urlData = urlMonitor._getUrlData(data.urlId);
            if (urlData) {
              const updateSuccess = await urlMonitor._setUrlData(data.urlId, {
                status: 'completed',
                checkStarted: 0, // 停止检查
              });

              // 记录日志
              defaultLogger.info(`URL任务 ${data.urlId} 状态已更新为 completed (请求者: ${clientId})`);

              // 如果有关联的业务任务ID，通知前端更新状态
              if (data.taskId) {
                wsServer.sendWatcherEventToClient('urlMonitorUpdate', {
                  urlId: data.urlId,
                  url: urlData.url,
                  status: 'completed',
                  previousStatus: urlData.status,
                  statusChanged: urlData.status !== 'completed',
                  taskId: data.taskId,
                  message: '任务已完成'
                }, clientId);
              }

              wsServer.sendToClient(client, {
                type: 'urlMonitor',
                action: 'updateTaskStatus',
                success: true,
                messageId: data.messageId,
                urlId: data.urlId,
                taskId: data.taskId,
                message: '任务状态已更新为completed'
              });
            } else {
              wsServer.sendToClient(client, {
                type: 'urlMonitor',
                action: 'updateTaskStatus',
                success: false,
                messageId: data.messageId,
                message: `未找到ID为 ${data.urlId} 的URL任务`
              });
            }
          } catch (error) {
            defaultLogger.error(`更新URL任务状态失败: ${error.message}`);
            wsServer.sendToClient(client, {
              type: 'urlMonitor',
              action: 'updateTaskStatus',
              success: false,
              messageId: data.messageId,
              message: `更新状态失败: ${error.message}`
            });
          }
          break

        default:
          wsServer.sendToClient(client, {
            type: 'error',
            messageId: data.messageId,
            message: `未知的urlMonitor操作: ${action}`
          })
      }
    })

    // 配置服务消息处理
    wsServer.registerMessageHandler('config', (client, action, data) => {
      switch (action) {
        case 'getAddonConfigPath':
          const addonConfigPath = configStore.getAddonConfigPath()
          wsServer.sendToClient(client, {
            type: 'config',
            action: 'addonConfigPath',
            success: true,
            messageId: data.messageId,
            addonConfigPath
          })
          break

        case 'setAddonConfigPath':
          if (!data || !data.path) {
            return wsServer.sendToClient(client, {
              type: 'config',
              action: 'setAddonConfigPath',
              success: false,
              messageId: data.messageId,
              message: '必须提供配置路径'
            })
          }

          const success = configStore.setAddonConfigPath(data.path)

          if (success) {
            // 发送事件通知路径已更改
            wsServer.broadcast({
              type: 'watcher',
              eventType: 'addonConfigPathChanged',
              data: { path: data.path }
            })
          }

          wsServer.sendToClient(client, {
            type: 'config',
            action: 'setAddonConfigPath',
            success,
            messageId: data.messageId,
            addonConfigPath: data.path,
            message: success ? '设置配置路径成功' : '无法设置配置路径'
          })
          break

        // 添加获取学科和年级选择
        case 'getSubjectAndStage':
          try {
            const subjectAndStage = configStore.getSubjectAndStage();
            wsServer.sendToClient(client, {
              type: 'config',
              action: 'subjectAndStage',
              success: true,
              messageId: data.messageId,
              data: subjectAndStage
            });
          } catch (error) {
            defaultLogger.error('获取学科和年级配置失败:', error);
            wsServer.sendToClient(client, {
              type: 'config',
              action: 'subjectAndStage',
              success: false,
              messageId: data.messageId,
              message: `获取学科和年级配置失败: ${error.message}`
            });
          }
          break;

        // 添加设置学科和年级选择
        case 'setSubjectAndStage':
          if (!data) {
            return wsServer.sendToClient(client, {
              type: 'config',
              action: 'setSubjectAndStage',
              success: false,
              messageId: data.messageId,
              message: '必须提供数据'
            });
          }
          (async () => {
            try {
              const setSuccess = await configStore.setSubjectAndStage(data.subject, data.stage);
              wsServer.sendToClient(client, {
                type: 'config',
                action: 'setSubjectAndStage',
                success: setSuccess,
                messageId: data.messageId,
                message: setSuccess ? '保存学科和年级配置成功' : '保存学科和年级配置失败'
              });

              // 广播学科和年级配置已更改事件，这样其他连接的客户端可以同步更新
              if (setSuccess) {
                wsServer.broadcast({
                  type: 'config',
                  eventType: 'subjectAndStageChanged',
                  data: {
                    subject: data.subject,
                    stage: data.stage
                  }
                });
              }
            } catch (error) {
              defaultLogger.error('设置学科和年级配置失败:', error);
              wsServer.sendToClient(client, {
                type: 'config',
                action: 'setSubjectAndStage',
                success: false,
                messageId: data.messageId,
                message: `设置学科和年级配置失败: ${error.message}`
              });
            }
          })();
          break;

        // 处理tooltip设置
        case 'getShowTooltip':
          try {
            const showTooltip = configStore.getShowTooltip();
            wsServer.sendToClient(client, {
              type: 'config',
              action: 'showTooltip',
              success: true,
              messageId: data.messageId,
              showTooltip
            });
          } catch (error) {
            defaultLogger.error('获取tooltip设置失败:', error);
            wsServer.sendToClient(client, {
              type: 'config',
              action: 'showTooltip',
              success: false,
              messageId: data.messageId,
              message: `获取tooltip设置失败: ${error.message}`
            });
          }
          break;

        case 'setShowTooltip':
          console.log(data)
          if (data.showTooltip === undefined) {
            return wsServer.sendToClient(client, {
              type: 'config',
              action: 'setShowTooltip',
              success: false,
              messageId: data.messageId,
              message: '必须提供showTooltip值'
            });
          }

          (async () => {
            try {
              const setSuccess = await configStore.setShowTooltip(data.showTooltip);

              wsServer.sendToClient(client, {
                type: 'config',
                action: 'setShowTooltip',
                success: setSuccess,
                messageId: data.messageId,
                message: setSuccess ? 'Tooltip设置保存成功' : 'Tooltip设置保存失败'
              });

              // 广播tooltip设置已更改事件，这样其他连接的客户端可以同步更新
              if (setSuccess) {
                wsServer.broadcast({
                  type: 'config',
                  eventType: 'tooltipSettingChanged',
                  data: {
                    showTooltip: data.showTooltip
                  }
                });
              }
            } catch (error) {
              defaultLogger.error('设置tooltip配置失败:', error);
              wsServer.sendToClient(client, {
                type: 'config',
                action: 'setShowTooltip',
                success: false,
                messageId: data.messageId,
                message: `设置tooltip配置失败: ${error.message}`
              });
            }
          })();
          break;

        // 处理保存方式设置
        case 'getSaveMethod':
          try {
            const saveMethod = configStore.getSaveMethod();
            wsServer.sendToClient(client, {
              type: 'config',
              action: 'saveMethod',
              success: true,
              messageId: data.messageId,
              saveMethod
            });
          } catch (error) {
            defaultLogger.error('获取保存方式设置失败:', error);
            wsServer.sendToClient(client, {
              type: 'config',
              action: 'saveMethod',
              success: false,
              messageId: data.messageId,
              message: `获取保存方式设置失败: ${error.message}`
            });
          }
          break;

        case 'setSaveMethod':
          if (!data.saveMethod) {
            return wsServer.sendToClient(client, {
              type: 'config',
              action: 'setSaveMethod',
              success: false,
              messageId: data.messageId,
              message: '必须提供saveMethod值'
            });
          }

          (async () => {
            try {
              const setSuccess = await configStore.setSaveMethod(data.saveMethod);

              wsServer.sendToClient(client, {
                type: 'config',
                action: 'setSaveMethod',
                success: setSuccess,
                messageId: data.messageId,
                message: setSuccess ? '保存方式设置保存成功' : '保存方式设置保存失败'
              });

              // 广播保存方式设置已更改事件，这样其他连接的客户端可以同步更新
              if (setSuccess) {
                wsServer.broadcast({
                  type: 'config',
                  eventType: 'saveMethodChanged',
                  data: {
                    saveMethod: data.saveMethod
                  }
                });
              }
            } catch (error) {
              defaultLogger.error('设置保存方式配置失败:', error);
              wsServer.sendToClient(client, {
                type: 'config',
                action: 'setSaveMethod',
                success: false,
                messageId: data.messageId,
                message: `设置保存方式配置失败: ${error.message}`
              });
            }
          })();
          break;

        default:
          wsServer.sendToClient(client, {
            type: 'error',
            messageId: data.messageId,
            message: `未知的config操作: ${action}`
          })
      }
    })

    // 版本信息处理器
    wsServer.registerMessageHandler('version', (client, action, data) => {
      switch (action) {
        case 'getInfo':
          // 发送版本信息
          wsServer.sendVersionInfo(client);
          break;

        case 'setEdition':
          // 设置版本类型（仅用于测试）
          if (data.edition && (data.edition === 'wanwei' || data.edition === 'hexin')) {
            // 更新全局环境配置
            if (global.ENV && global.ENV.config) {
              global.ENV.config.EDITION = data.edition;

              // 向所有客户端广播版本更新
              wsServer.broadcastVersionInfo();

              wsServer.sendToClient(client, {
                type: 'version',
                action: 'setEdition',
                success: true,
                messageId: data.messageId,
                message: `版本已切换到: ${data.edition}`,
                data: { edition: data.edition }
              });
            } else {
              wsServer.sendToClient(client, {
                type: 'version',
                action: 'setEdition',
                success: false,
                messageId: data.messageId,
                message: '无法访问环境配置'
              });
            }
          } else {
            wsServer.sendToClient(client, {
              type: 'version',
              action: 'setEdition',
              success: false,
              messageId: data.messageId,
              message: '无效的版本类型，必须是 wanwei 或 hexin'
            });
          }
          break;

        default:
          wsServer.sendToClient(client, {
            type: 'error',
            messageId: data.messageId,
            message: `未知的version操作: ${action}`
          });
      }
    })

    // 认证服务消息处理
    wsServer.registerMessageHandler('auth', async (client, action, data) => {
      switch (action) {
        case 'login':
          if (!data || !data.username || !data.password) {
            defaultLogger.warn('登录尝试缺少用户名或密码')
            return wsServer.sendToClient(client, {
              type: 'auth',
              action: 'login',
              success: false,
              messageId: data.messageId,
              message: '必须提供用户名和密码'
            })
          }

          defaultLogger.info(`开始处理登录请求: ${data.username}, messageId: ${data.messageId}`)
          try {
            const result = await authProxy.proxyLogin({
              username: data.username,
              password: data.password
            })
            defaultLogger.info(`UC API登录结果: ${result.success ? '成功' : '失败'}`, {
              hasData: !!result.data,
              hasCookies: !!result.cookies && Object.keys(result.cookies || {}).length > 0,
              message: result.message
            })

            if (result.success) {
              defaultLogger.info('登录成功，开始权限检查')

              // 登录成功后，调用 proxyGetOwn 检查权限
              try {
                const ownResult = await authProxy.proxyGetOwn()

                if (ownResult.success) {
                  defaultLogger.info('权限检查通过，发送登录成功事件')

                  // 发出登录成功事件，用于通知桌面端应用
                  authEvents.emit('login-success', ownResult.data)

                  // 通过WebSocket广播登录成功事件
                  wsServer.broadcast({
                    type: 'auth',
                    eventType: 'login-success',
                    data: ownResult.data,
                    timestamp: new Date().toISOString()
                  })

                  // 向请求客户端发送登录成功响应
                  wsServer.sendToClient(client, {
                    type: 'auth',
                    action: 'login',
                    success: true,
                    messageId: data.messageId,
                    data: ownResult.data,
                    cookies: ownResult.data.cookies,
                    message: result.message || '登录成功'
                  })
                } else {
                  defaultLogger.warn(`权限检查失败: ${ownResult.message}, code: ${ownResult.code}`)
                  // 传递权限检查失败的详细信息，包括错误码
                  wsServer.sendToClient(client, {
                    type: 'auth',
                    action: 'login',
                    success: false,
                    messageId: data.messageId,
                    message: ownResult.message,
                    code: ownResult.code,
                    data: ownResult.data
                  })
                }
              } catch (permissionError) {
                defaultLogger.error('权限检查过程中发生错误:', permissionError)
                wsServer.sendToClient(client, {
                  type: 'auth',
                  action: 'login',
                  success: false,
                  messageId: data.messageId,
                  message: '权限检查失败，请稍后重试'
                })
              }
            } else {
              defaultLogger.warn(`登录失败: ${result.message || '未知错误'}`)
              wsServer.sendToClient(client, {
                type: 'auth',
                action: 'login',
                success: false,
                messageId: data.messageId,
                message: result.message
              })
            }
          } catch (error) {
            defaultLogger.error('UC API登录代理错误:', error)
            wsServer.sendToClient(client, {
              type: 'auth',
              action: 'login',
              success: false,
              messageId: data.messageId,
              message: '服务器错误，请稍后重试'
            })
          }
          break

        case 'logout':
          try {
            const result = await authProxy.proxyLogout()

            // 发出登出事件，用于通知桌面端应用
            defaultLogger.info('发送登出事件到桌面应用')
            authEvents.emit('logout')

            // 通过WebSocket广播登出事件
            defaultLogger.info('广播登出事件到所有客户端')
            wsServer.broadcast({
              type: 'auth',
              eventType: 'logout',
              timestamp: new Date().toISOString()
            })

            wsServer.sendToClient(client, {
              type: 'auth',
              action: 'logout',
              success: true,
              messageId: data.messageId,
              message: result.message || '退出登录成功'
            })
          } catch (error) {
            defaultLogger.error('UC API退出登录代理错误:', error)
            wsServer.sendToClient(client, {
              type: 'auth',
              action: 'logout',
              success: false,
              messageId: data.messageId,
              message: '服务器错误，请稍后重试'
            })
          }
          break

        case 'getOwn':
          try {
            const result = await authProxy.proxyGetOwn()

            if (result.success) {
              // 发出登录成功事件，用于通知桌面端应用
              authEvents.emit('login-success', result.data)

              wsServer.sendToClient(client, {
                type: 'auth',
                action: 'getOwn',
                success: true,
                messageId: data.messageId,
                data: result.data,
                cookies: result.cookies
              })
            } else {
              wsServer.sendToClient(client, {
                type: 'auth',
                action: 'getOwn',
                success: false,
                messageId: data.messageId,
                message: result.message || '未登录或会话已过期',
                code: result.code,
                data: result.data
              })
            }
          } catch (error) {
            defaultLogger.error('UC API获取用户信息代理错误:', error)
            wsServer.sendToClient(client, {
              type: 'auth',
              action: 'getOwn',
              success: false,
              messageId: data.messageId,
              message: '服务器错误，请稍后重试'
            })
          }
          break

        case 'refreshSession':
          try {
            const result = await authProxy.refreshSession()

            if (result.success) {
              // 发出登录成功事件，用于通知桌面端应用
              authEvents.emit('login-success', result.data)

              wsServer.sendToClient(client, {
                type: 'auth',
                action: 'refreshSession',
                success: true,
                messageId: data.messageId,
                data: result.data,
                cookies: result.cookies
              })
            } else {
              wsServer.sendToClient(client, {
                type: 'auth',
                action: 'refreshSession',
                success: false,
                messageId: data.messageId,
                message: result.message || '未登录或会话已过期',
                code: result.code,
                data: result.data
              })
            }
          } catch (error) {
            defaultLogger.error('UC API会话刷新错误:', error)
            wsServer.sendToClient(client, {
              type: 'auth',
              action: 'refreshSession',
              success: false,
              messageId: data.messageId,
              message: '服务器错误，请稍后重试'
            })
          }
          break

        default:
          wsServer.sendToClient(client, {
            type: 'error',
            message: `未知的auth操作: ${action}`
          })
      }
    })

    // 添加健康检查消息处理
    wsServer.registerMessageHandler('health', (client, action, data) => {
      try {
        // 检查是否使用了老的客户端格式
        const isLegacyClient = data && typeof data.legacyFormat !== 'undefined'
        defaultLogger.debug(
          `处理health消息: action=${action}, messageId=${data.messageId}, 客户端类型=${isLegacyClient ? '旧版' : '新版'}`
        )

        if (action === 'getStatus') {
          // 构建健康状态数据
          const healthData = {
            status: 'ok',
            timestamp: new Date().toISOString(),
            services: {
              watcher: {
                running:
                  typeof fileWatcher.isRunning === 'function'
                    ? fileWatcher.isRunning()
                    : fileWatcher.isRunning,
                watchDir: fileWatcher.getWatchDir()
              },
              urlMonitor: {
                running: true,
                taskCount: urlMonitor.getMonitoringCount()
              },
              websocket: {
                running: wsServer.isRunning(),
                clientCount: wsServer.getClientCount()
              }
            }
          }

          // 根据客户端类型选择不同的响应格式
          let response
          if (isLegacyClient) {
            // 旧客户端期望的格式
            response = {
              type: 'health',
              action: action, // 使用原始请求的action
              success: true,
              messageId: data.messageId,
              result: healthData
            }
          } else {
            // 新客户端扁平化格式
            response = {
              type: 'health',
              action: action, // 使用原始请求的action
              success: true,
              messageId: data.messageId,
              status: healthData.status,
              timestamp: healthData.timestamp,
              services: healthData.services
            }
          }
          // 发送响应
          wsServer.sendToClient(client, response)
          defaultLogger.debug(`发送health状态响应: messageId=${data.messageId}`)
        } else {
          defaultLogger.warn(`未知的health操作: ${action}, messageId=${data.messageId}`)
          wsServer.sendToClient(client, {
            type: 'health',
            action: action,
            success: false,
            messageId: data.messageId,
            message: `未知的health操作: ${action}`
          })
        }
      } catch (error) {
        console.log('处理health类型消息时出错:', error)
        defaultLogger.error('处理health类型消息时出错:', error)
        wsServer.sendToClient(client, {
          type: 'health',
          action: action || 'unknown',
          success: false,
          messageId: data.messageId,
          message: '处理健康检查请求时出错',
          error: error.message
        })
      }
    })

    // 添加日志同步消息处理
    const LoggerHandler = require('./services/handlers/loggerHandler');
    const loggerHandler = new LoggerHandler(wsServer);

    wsServer.registerMessageHandler('logger', (client, action, data) => {
      loggerHandler.handleMessage(client, action, data);
    });
  }

  // 返回server和app实例以便主进程可以控制它们
  return {
    server,
    app,
    port,
    authEvents, // 暴露认证事件发射器给主进程
    wsServer, // 暴露WebSocket服务器给主进程
    stop: () => {
      try {
        // 停止文件监控服务
        fileWatcher.stop()
        // 停止WebSocket服务
        wsServer.close()
        // 关闭HTTP服务器
        server.close()
        return true
      } catch (err) {
        defaultLogger.error('关闭服务器时出错:', err)
        return false
      }
    }
  }
}

module.exports = { startServer, authEvents }
