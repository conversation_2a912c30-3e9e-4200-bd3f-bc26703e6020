{"name": "wps-addon-server", "version": "1.1.23", "description": "WPS", "main": "main.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "electron:wanwei": "chcp 65001 && cross-env APP_EDITION=wanwei electron .", "electron:hexin": "chcp 65001 && cross-env APP_EDITION=hexin electron .", "electron:wwsenior": "chcp 65001 && cross-env APP_EDITION=wwsenior electron .", "electron": "npm run electron:wanwei", "dev:wanwei": "cross-env APP_EDITION=wanwei nodemon server.js", "dev:hexin": "cross-env APP_EDITION=hexin nodemon server.js", "dev:wwsenior": "cross-env APP_EDITION=wwsenior nodemon server.js", "build": "electron-builder --config electron-builder.yml --win", "build:dir": "cross-env NODE_ENV=production electron-builder --config electron-builder.yml --win", "build:hexin": "electron-builder --config electron-builder-hexin.yml --win", "build:wwsenior": "electron-builder --config electron-builder-wwsenior.yml --win", "build:multi": "node scripts/build-multi-version.js", "build:optimized": "cross-env NODE_ENV=production node scripts/pre-build-check.js && node scripts/build-multi-version.js && node scripts/upload-dist.js && node scripts/create-symlinks.js", "upload": "node scripts/upload-dist.js", "symlinks": "node scripts/create-symlinks.js", "deploy": "npm run upload && npm run symlinks", "release-notes": "node scripts/generate-release-notes.js", "release-notes:auto": "node scripts/generate-release-notes-advanced.js", "update-version": "node scripts/update-version-info.js"}, "dependencies": {"ali-oss": "^6.18.1", "axios": "^1.9.0", "better-sqlite3": "^11.10.0", "body-parser": "^1.20.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "electron-updater": "^6.6.2", "express": "^4.18.2", "jszip": "^3.10.1", "tcp-port-used": "^1.0.2", "uuid": "^9.0.1", "ws": "^8.16.0"}, "devDependencies": {"cross-env": "^7.0.3", "electron": "^30.0.0", "electron-builder": "^26.0.12", "electron-package": "^0.1.0", "electron-rebuild": "^3.2.9", "fs-extra": "^11.2.0", "nodemon": "^3.0.2"}, "build": {"appId": "com.hexin.wps-addon-server", "productName": "万唯AI编辑WPS插件服务", "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}"], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}, {"from": "server.js", "to": "server.js"}, {"from": "services", "to": "services"}], "asar": false, "win": {"target": "nsis", "icon": "assets/ww.png"}, "nsis": {"unicode": true, "oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true}}, "optionalDependencies": {"canvas": "^3.1.0"}}